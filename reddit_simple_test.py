#!/usr/bin/env python3
"""
简单的Reddit API测试
用于验证基本的Reddit API访问
"""

import os
import praw
import prawcore
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_basic_reddit_access():
    """测试基本的Reddit API访问"""
    
    print("测试Reddit API基本访问...")
    
    # 获取配置
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    user_agent = os.getenv('REDDIT_USER_AGENT', 'testscript by u/testuser')
    
    print(f"Client ID: {client_id[:10]}...")
    print(f"User Agent: {user_agent}")
    
    # 方法1: 只读模式 (推荐用于数据收集)
    print("\n=== 测试只读模式 ===")
    try:
        reddit = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent
        )
        
        print("✓ Reddit实例创建成功")
        
        # 测试访问一个简单的子版块
        subreddit = reddit.subreddit('test')
        print(f"✓ 访问r/test成功: {subreddit.display_name}")
        
        # 尝试获取一个帖子
        submissions = list(subreddit.hot(limit=1))
        if submissions:
            post = submissions[0]
            print(f"✓ 获取帖子成功: {post.title[:50]}...")
        else:
            print("⚠ 没有找到帖子")
            
        return True
        
    except prawcore.exceptions.ResponseException as e:
        print(f"✗ API响应错误 ({e.response.status_code}): {e}")
        if e.response.status_code == 401:
            print("  可能原因: Client ID或Client Secret错误")
        elif e.response.status_code == 403:
            print("  可能原因: 应用类型设置错误或被Reddit限制")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {e}")
        return False

def test_different_user_agents():
    """测试不同的User-Agent"""
    
    print("\n=== 测试不同User-Agent ===")
    
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    
    user_agents = [
        "testscript/1.0",
        "python:testscript:v1.0 (by /u/testuser)",
        "AI-Hedge-Fund-Bot/1.0",
        "reddit-data-collector/1.0 by testuser"
    ]
    
    for ua in user_agents:
        print(f"\n测试User-Agent: {ua}")
        try:
            reddit = praw.Reddit(
                client_id=client_id,
                client_secret=client_secret,
                user_agent=ua
            )
            
            # 简单测试
            subreddit = reddit.subreddit('test')
            _ = subreddit.display_name
            print(f"✓ 成功")
            return True
            
        except Exception as e:
            print(f"✗ 失败: {e}")
    
    return False

def test_reddit_app_type():
    """测试Reddit应用类型建议"""
    
    print("\n=== Reddit应用设置检查 ===")
    print("请确认您的Reddit应用设置:")
    print("1. 访问: https://www.reddit.com/prefs/apps")
    print("2. 应用类型应该是: 'script' (不是'web app')")
    print("3. redirect uri可以设置为: http://localhost:8080")
    print("4. 确保应用状态是'active'")
    
    print("\n如果应用类型不正确，请:")
    print("1. 删除现有应用")
    print("2. 创建新的'script'类型应用")
    print("3. 更新.env文件中的CLIENT_ID和CLIENT_SECRET")

if __name__ == '__main__':
    success = test_basic_reddit_access()
    
    if not success:
        test_different_user_agents()
        test_reddit_app_type()
        
        print("\n=== 解决方案建议 ===")
        print("1. 检查Reddit应用类型是否为'script'")
        print("2. 确认Client ID和Secret正确")
        print("3. 尝试创建新的Reddit应用")
        print("4. 检查网络连接和防火墙设置")
        print("5. 如果问题持续，可能需要等待或联系Reddit支持")
