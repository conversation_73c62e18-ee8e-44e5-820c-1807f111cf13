#!/usr/bin/env python3
"""
Reddit API 诊断工具
用于诊断Reddit API 403错误的根本原因
"""

import os
import praw
import prawcore
from dotenv import load_dotenv
import logging
from datetime import datetime

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_reddit_api():
    """测试Reddit API配置和权限"""
    
    print("="*60)
    print("Reddit API 诊断工具")
    print("="*60)
    
    # 1. 检查环境变量配置
    print("\n1. 检查环境变量配置:")
    client_id = os.getenv('REDDIT_CLIENT_ID')
    client_secret = os.getenv('REDDIT_CLIENT_SECRET')
    user_agent = os.getenv('REDDIT_USER_AGENT')
    username = os.getenv('REDDIT_USERNAME')
    password = os.getenv('REDDIT_PASSWORD')
    
    print(f"   REDDIT_CLIENT_ID: {'✓' if client_id else '✗'} ({'已设置' if client_id else '未设置'})")
    print(f"   REDDIT_CLIENT_SECRET: {'✓' if client_secret else '✗'} ({'已设置' if client_secret else '未设置'})")
    print(f"   REDDIT_USER_AGENT: {'✓' if user_agent else '✗'} ({user_agent if user_agent else '未设置'})")
    print(f"   REDDIT_USERNAME: {'✓' if username else '✗'} ({username if username else '未设置'})")
    print(f"   REDDIT_PASSWORD: {'✓' if password else '✗'} ({'已设置' if password else '未设置'})")
    
    if not client_id or not client_secret:
        print("\n❌ 错误: Reddit API凭据缺失!")
        return False
    
    # 2. 测试不同的认证方式
    print("\n2. 测试Reddit API认证:")
    
    # 测试只读模式 (不需要用户名密码)
    print("\n   2.1 测试只读模式 (无用户认证):")
    try:
        reddit_readonly = praw.Reddit(
            client_id=client_id,
            client_secret=client_secret,
            user_agent=user_agent
        )
        
        # 测试基本访问
        test_subreddit = reddit_readonly.subreddit('python')
        print(f"      ✓ 成功创建只读Reddit实例")
        print(f"      ✓ 测试子版块访问: r/python - {test_subreddit.display_name}")
        print(f"      ✓ 子版块订阅者数: {test_subreddit.subscribers}")
        
    except Exception as e:
        print(f"      ✗ 只读模式失败: {e}")
        return False
    
    # 测试用户认证模式
    if username and password:
        print("\n   2.2 测试用户认证模式:")
        try:
            reddit_user = praw.Reddit(
                client_id=client_id,
                client_secret=client_secret,
                user_agent=user_agent,
                username=username,
                password=password
            )
            
            # 测试用户信息
            user = reddit_user.user.me()
            print(f"      ✓ 用户认证成功: {user.name}")
            print(f"      ✓ 用户karma: {user.link_karma + user.comment_karma}")
            
        except prawcore.exceptions.ResponseException as e:
            if e.response.status_code == 401:
                print(f"      ✗ 用户认证失败 (401): 用户名或密码错误")
            else:
                print(f"      ✗ 用户认证失败: {e}")
        except Exception as e:
            print(f"      ✗ 用户认证失败: {e}")
    else:
        print("\n   2.2 跳过用户认证测试 (用户名或密码未设置)")
    
    # 3. 测试目标子版块访问
    print("\n3. 测试目标子版块访问:")
    target_subreddits = ['stocks', 'investing', 'wallstreetbets', 'SecurityAnalysis']
    
    for subreddit_name in target_subreddits:
        print(f"\n   3.{target_subreddits.index(subreddit_name)+1} 测试 r/{subreddit_name}:")
        try:
            subreddit = reddit_readonly.subreddit(subreddit_name)
            
            # 测试基本信息访问
            print(f"      ✓ 子版块名称: {subreddit.display_name}")
            print(f"      ✓ 订阅者数: {subreddit.subscribers}")
            print(f"      ✓ 子版块类型: {'公开' if subreddit.subreddit_type == 'public' else subreddit.subreddit_type}")
            
            # 测试帖子访问
            try:
                posts = list(subreddit.new(limit=1))
                if posts:
                    post = posts[0]
                    print(f"      ✓ 最新帖子访问成功: {post.title[:50]}...")
                    print(f"      ✓ 帖子作者: {post.author}")
                    print(f"      ✓ 帖子时间: {datetime.fromtimestamp(post.created_utc)}")
                else:
                    print(f"      ⚠ 没有找到帖子")
                    
            except prawcore.exceptions.Forbidden as e:
                print(f"      ✗ 帖子访问被禁止 (403): 可能是私有或受限子版块")
            except prawcore.exceptions.ResponseException as e:
                if e.response.status_code == 403:
                    print(f"      ✗ 帖子访问被禁止 (403): {e}")
                else:
                    print(f"      ✗ 帖子访问失败 ({e.response.status_code}): {e}")
            except Exception as e:
                print(f"      ✗ 帖子访问失败: {e}")
                
        except prawcore.exceptions.NotFound:
            print(f"      ✗ 子版块不存在")
        except prawcore.exceptions.Forbidden:
            print(f"      ✗ 子版块访问被禁止 (私有或受限)")
        except Exception as e:
            print(f"      ✗ 子版块访问失败: {e}")
    
    # 4. 测试API限制和配额
    print("\n4. 测试API限制:")
    try:
        # 检查API限制状态
        print(f"   ✓ API客户端类型: {'只读' if not (username and password) else '用户认证'}")
        print(f"   ✓ User-Agent: {user_agent}")
        
        # 测试多次请求
        print(f"   ✓ 测试连续请求...")
        for i in range(3):
            test_sub = reddit_readonly.subreddit('python')
            _ = test_sub.display_name
            print(f"      请求 {i+1}: 成功")
            
    except Exception as e:
        print(f"   ✗ API限制测试失败: {e}")
    
    # 5. 提供解决方案建议
    print("\n5. 解决方案建议:")
    print("   基于诊断结果，可能的解决方案:")
    print("   • 如果只读模式工作但用户认证失败:")
    print("     - 检查Reddit用户名和密码是否正确")
    print("     - 确认Reddit应用类型设置为'script'")
    print("     - 如果使用Google登录，需要设置Reddit密码")
    print("   • 如果特定子版块403错误:")
    print("     - 子版块可能是私有或受限的")
    print("     - 尝试使用其他公开子版块")
    print("     - 检查是否被子版块封禁")
    print("   • 如果所有请求都403:")
    print("     - 检查Reddit API应用设置")
    print("     - 确认User-Agent格式正确")
    print("     - 检查IP是否被Reddit限制")
    
    return True

if __name__ == '__main__':
    test_reddit_api()
