from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from src.graph.state import AgentState, show_agent_reasoning
from src.utils.progress import progress
from src.utils.llm import call_llm
from src.tools.api import get_company_news, get_insider_trades
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
from pathlib import Path


def _save_local_social_media_input_data(agent_name: str, ticker: str, data: List[Dict[str, Any]], metadata: dict | None = None):
    """
    保存本地社交媒体数据的输入数据到全局输入数据管理器

    Args:
        agent_name: 代理名称
        ticker: 股票代码
        data: 本地社交媒体数据
        metadata: 元数据
    """
    try:
        from src.utils.input_data_manager import get_global_input_data_manager
        manager = get_global_input_data_manager()
        if manager:
            # 确保元数据包含正确的数据源标识
            if metadata is None:
                metadata = {}
            metadata["source"] = "local"
            manager.save_api_data(agent_name, ticker, "load_local_social_media_data", data, metadata)
    except Exception as e:
        # 静默处理错误，不影响主要功能
        pass


class SocialMediaAnalysisSignal(BaseModel):
    """Pydantic model for social media analysis LLM output"""
    signal: str = Field(description="Trading signal: 'bullish', 'bearish', or 'neutral'")
    confidence: float = Field(description="Confidence level from 0 to 100")
    reasoning: str = Field(description="Detailed reasoning for the signal")
    public_sentiment_analysis: str = Field(description="Analysis of public sentiment and social perception")
    insider_activity_analysis: str = Field(description="Analysis of insider trading patterns and implications")
    attention_analysis: str = Field(description="Analysis of public attention and buzz levels")
    sentiment_momentum_analysis: str = Field(description="Analysis of sentiment momentum and trend changes")
    social_influence_analysis: str = Field(description="Analysis of social influence factors and viral potential")


def load_local_social_media_data(ticker: str, end_date: str, days_back: int = 7) -> List[Dict[str, Any]]:
    """
    Load local social media data from ticker-specific directories

    This function implements dynamic loading of local social media data files based on the ticker symbol.
    It follows the same pattern as existing local news data handling for AAPL, MSFT, and NVDA stocks.

    File Structure Expected:
    - {TICKER}_social_media/ (e.g., AAPL_social_media/, NVDA_social_media/)
    - Files organized by date: TICKER_YYYY-MM-DD.json (e.g., AAPL_2024-01-15.json)
    - JSON format compatible with existing social_media_data structure

    Args:
        ticker: Stock ticker symbol (e.g., 'AAPL', 'NVDA', 'MSFT')
        end_date: End date in YYYY-MM-DD format
        days_back: Number of days to look back for data (default: 7)

    Returns:
        List of social media posts with engagement metrics and sentiment data
    """
    try:
        from src.config.social_media_config import get_social_media_config
        config = get_social_media_config()

        if not config.should_use_local_social_media():
            progress.update_status("social_media_analyst_agent", ticker, "Local social media mode disabled, skipping local data")
            return []

        all_posts = []
        end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
        successful_dates = []
        failed_dates = []

        progress.update_status("social_media_analyst_agent", ticker, f"Loading local social media data for {days_back + 1} days")

        # Search for data within the specified date range
        for i in range(days_back + 1):
            check_date = end_datetime - timedelta(days=i)
            date_str = check_date.strftime("%Y-%m-%d")

            # Get social media data file path for the ticker
            file_path = config.get_social_media_file_path(ticker, date_str, "reddit")

            if file_path and file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        posts = json.load(f)

                    if isinstance(posts, list) and len(posts) > 0:
                        # Validate and process posts
                        valid_posts = []
                        for post in posts:
                            if isinstance(post, dict) and post.get('title'):
                                # Ensure required fields exist with defaults
                                processed_post = {
                                    'title': post.get('title', ''),
                                    'content': post.get('content', post.get('selftext', '')),
                                    'created_time': post.get('created_time', date_str + ' 00:00:00'),
                                    'platform': post.get('platform', 'reddit'),
                                    'sentiment': post.get('sentiment', 'neutral'),
                                    'engagement_score': post.get('engagement_score', 0),
                                    'upvotes': post.get('upvotes', post.get('score', 0)),
                                    'num_comments': post.get('num_comments', 0),
                                    'subreddit': post.get('subreddit', 'unknown'),
                                    'author': post.get('author', 'unknown'),
                                    'url': post.get('url', ''),
                                    'ticker': ticker,
                                    'date': date_str
                                }
                                valid_posts.append(processed_post)

                        all_posts.extend(valid_posts)
                        successful_dates.append(date_str)
                        progress.update_status("social_media_analyst_agent", ticker, f"Loaded {len(valid_posts)} posts from {date_str}")
                    else:
                        progress.update_status("social_media_analyst_agent", ticker, f"No valid posts found in {file_path}")

                except json.JSONDecodeError as e:
                    failed_dates.append(date_str)
                    progress.update_status("social_media_analyst_agent", ticker, f"JSON decode error for {date_str}: {e}")
                    continue
                except Exception as e:
                    failed_dates.append(date_str)
                    progress.update_status("social_media_analyst_agent", ticker, f"Failed to load {file_path}: {e}")
                    continue
            else:
                # File doesn't exist for this date
                failed_dates.append(date_str)

        # Implement intelligent fallback mechanism
        if len(all_posts) == 0 and len(successful_dates) == 0:
            progress.update_status("social_media_analyst_agent", ticker, "No data found for target dates, trying intelligent fallback search")

            # First, try to find data within a reasonable range around the target date
            # This helps avoid using data from completely different time periods
            extended_search_days = min(30, days_back * 3)  # Search up to 30 days or 3x the original range

            for i in range(1, extended_search_days + 1):
                # Search both forward and backward from end_date
                for direction in [-1, 1]:  # -1 for backward, 1 for forward
                    check_date = end_datetime + timedelta(days=direction * i)
                    date_str = check_date.strftime("%Y-%m-%d")
                    file_path = config.get_social_media_file_path(ticker, date_str, "reddit")

                    if file_path and file_path.exists():
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                posts = json.load(f)

                            if isinstance(posts, list) and len(posts) > 0:
                                progress.update_status("social_media_analyst_agent", ticker, f"Found nearby data for {date_str} ({abs(i)} days from target)")
                                # Process posts similar to main loop
                                valid_posts = []
                                for post in posts:
                                    if isinstance(post, dict) and post.get('title'):
                                        processed_post = {
                                            'title': post.get('title', ''),
                                            'content': post.get('content', post.get('selftext', '')),
                                            'created_time': post.get('created_time', date_str + ' 00:00:00'),
                                            'platform': post.get('platform', 'reddit'),
                                            'sentiment': post.get('sentiment', 'neutral'),
                                            'engagement_score': post.get('engagement_score', 0),
                                            'upvotes': post.get('upvotes', post.get('score', 0)),
                                            'num_comments': post.get('num_comments', 0),
                                            'subreddit': post.get('subreddit', 'unknown'),
                                            'author': post.get('author', 'unknown'),
                                            'url': post.get('url', ''),
                                            'ticker': ticker,
                                            'date': date_str
                                        }
                                        valid_posts.append(processed_post)

                                all_posts.extend(valid_posts)
                                successful_dates.append(date_str)
                                break  # Found data, stop searching in this direction
                        except Exception as e:
                            continue

                # If we found data, stop the extended search
                if all_posts:
                    break

            # If still no data found, use the original fallback mechanism
            if len(all_posts) == 0:
                progress.update_status("social_media_analyst_agent", ticker, "No nearby data found, using original fallback mechanism")
                all_posts = _load_fallback_social_media_data(ticker, config, days_back=30, target_date=end_date)

        # Log summary and save input data if successful
        if successful_dates:
            progress.update_status("social_media_analyst_agent", ticker, f"Successfully loaded {len(all_posts)} posts from {len(successful_dates)} dates")
            # Save input data with local source identifier
            _save_local_social_media_input_data("social_media_analyst_agent", ticker, all_posts, {
                "end_date": end_date,
                "days_back": days_back,
                "successful_dates": successful_dates,
                "source": "local"
            })
        elif len(all_posts) > 0:
            progress.update_status("social_media_analyst_agent", ticker, f"Loaded {len(all_posts)} posts using fallback mechanism")
            # Save input data with local source identifier (fallback)
            _save_local_social_media_input_data("social_media_analyst_agent", ticker, all_posts, {
                "end_date": end_date,
                "days_back": days_back,
                "fallback_used": True,
                "source": "local"
            })
        else:
            progress.update_status("social_media_analyst_agent", ticker, f"No social media data found for {ticker}")

        return all_posts

    except ImportError:
        progress.update_status("social_media_analyst_agent", ticker, "Social media configuration module not available")
        return []
    except Exception as e:
        progress.update_status("social_media_analyst_agent", ticker, f"Error loading local social media data: {e}")
        return []


def _load_fallback_social_media_data(ticker: str, config, days_back: int = 365, target_date: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Fallback mechanism to load the most recent 3 available social media data files
    when exact date data is missing.

    This function searches for any available social media data files for the ticker
    and loads the 3 most recent ones found, prioritizing dates closest to the target date.

    Args:
        ticker: Stock ticker symbol
        config: Social media configuration object
        days_back: Number of days to search backwards (default: 365 for broader search)
        target_date: Target date for backtesting (YYYY-MM-DD format). If provided,
                    search will be based on this date instead of current date.

    Returns:
        List of social media posts from the most recent available files
    """
    try:
        available_files = []

        # First, try to find files by scanning the directory directly
        directory = config.get_social_media_directory(ticker, "reddit")
        if directory:
            base_dir = Path(config.config["base_data_directory"])
            ticker_dir = base_dir / directory

            if ticker_dir.exists():
                # Scan for JSON files in the directory
                for file_path in ticker_dir.glob("*.json"):
                    try:
                        # Check if file has valid data
                        with open(file_path, 'r', encoding='utf-8') as f:
                            posts = json.load(f)
                        if isinstance(posts, list) and len(posts) > 0:
                            # Extract date from filename if possible
                            filename = file_path.name
                            if "_" in filename:
                                # Try to extract date from TICKER_YYYY-MM-DD.json format
                                date_part = filename.split("_", 1)[1].replace(".json", "")
                                try:
                                    # Validate date format
                                    datetime.strptime(date_part, "%Y-%m-%d")
                                    available_files.append((date_part, file_path, len(posts)))
                                except ValueError:
                                    # If date parsing fails, use file modification time
                                    mod_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                                    date_str = mod_time.strftime("%Y-%m-%d")
                                    available_files.append((date_str, file_path, len(posts)))
                    except Exception as e:
                        progress.update_status("social_media_analyst_agent", ticker, f"Fallback: Error reading {file_path}: {e}")
                        continue

        # If directory scan didn't work, fall back to date-based search
        if not available_files:
            # Use target_date if provided, otherwise fall back to current date
            if target_date:
                try:
                    search_start_date = datetime.strptime(target_date, "%Y-%m-%d")
                    progress.update_status("social_media_analyst_agent", ticker, f"Fallback search starting from target date: {target_date}")
                except ValueError:
                    search_start_date = datetime.now()
                    progress.update_status("social_media_analyst_agent", ticker, f"Invalid target date format, using current date for fallback search")
            else:
                search_start_date = datetime.now()
                progress.update_status("social_media_analyst_agent", ticker, "No target date provided, using current date for fallback search")

            # Search for available files in the past days_back days from search_start_date
            for i in range(days_back):
                check_date = search_start_date - timedelta(days=i)
                date_str = check_date.strftime("%Y-%m-%d")
                file_path = config.get_social_media_file_path(ticker, date_str, "reddit")

                if file_path and file_path.exists():
                    try:
                        # Check if file has valid data
                        with open(file_path, 'r', encoding='utf-8') as f:
                            posts = json.load(f)
                        if isinstance(posts, list) and len(posts) > 0:
                            available_files.append((date_str, file_path, len(posts)))
                    except:
                        continue

        if not available_files:
            progress.update_status("social_media_analyst_agent", ticker, "No fallback social media data files found")
            return []

        # Sort by date (most recent first) and take the 3 most recent files
        available_files.sort(key=lambda x: x[0], reverse=True)
        recent_files = available_files[:3]

        all_posts = []
        for date_str, file_path, _ in recent_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    posts = json.load(f)

                if isinstance(posts, list):
                    # Process posts similar to main function
                    valid_posts = []
                    for post in posts:
                        if isinstance(post, dict) and post.get('title'):
                            processed_post = {
                                'title': post.get('title', ''),
                                'content': post.get('content', post.get('selftext', '')),
                                'created_time': post.get('created_time', date_str + ' 00:00:00'),
                                'platform': post.get('platform', 'reddit'),
                                'sentiment': post.get('sentiment', 'neutral'),
                                'engagement_score': post.get('engagement_score', 0),
                                'upvotes': post.get('upvotes', post.get('score', 0)),
                                'num_comments': post.get('num_comments', 0),
                                'subreddit': post.get('subreddit', 'unknown'),
                                'author': post.get('author', 'unknown'),
                                'url': post.get('url', ''),
                                'ticker': ticker,
                                'date': date_str
                            }
                            valid_posts.append(processed_post)

                    all_posts.extend(valid_posts)
                    progress.update_status("social_media_analyst_agent", ticker, f"Fallback: loaded {len(valid_posts)} posts from {date_str}")

            except Exception as e:
                progress.update_status("social_media_analyst_agent", ticker, f"Fallback: failed to load {file_path}: {e}")
                continue

        progress.update_status("social_media_analyst_agent", ticker, f"Fallback mechanism loaded {len(all_posts)} posts from {len(recent_files)} recent files")
        return all_posts

    except Exception as e:
        progress.update_status("social_media_analyst_agent", ticker, f"Fallback mechanism failed: {e}")
        return []


##### Social Media Analyst Agent #####
def social_media_analyst_agent(state: AgentState):
    """
    Analyzes historical local social media sentiment and public perception using LLM to generate trading signals.

    This agent has been enhanced to support local social media data reading functionality with:
    - Dynamic loading of local social media data files based on ticker symbol
    - Support for {TICKER}_social_media/ folder structure (e.g., AAPL_social_media/, NVDA_social_media/)
    - JSON format files organized by date: TICKER_YYYY-MM-DD.json (e.g., AAPL_2024-01-15.json)
    - Robust error handling when data is unavailable
    - Fallback mechanism using most recent 3 available data files when exact date data is missing
    - Graceful degradation with neutral signals when no data is available
    - Integration with --use_local_social_media backtester parameter

    The agent analyzes historical social media data instead of real-time API data, providing
    sentiment analysis based on archived social media posts and engagement metrics.
    """
    data = state["data"]
    end_date = data["end_date"]
    tickers = data["tickers"]
    model_name = state["metadata"]["model_name"]
    model_provider = state["metadata"]["model_provider"]

    # Initialize social media analysis for each ticker
    social_analysis = {}

    for ticker in tickers:
        progress.update_status("social_media_analyst_agent", ticker, "Fetching social sentiment data")

        # Try to load local social media data first
        local_social_media_data = load_local_social_media_data(ticker, end_date, days_back=7)

        # Get company news as proxy for social sentiment (if no local social media data)
        company_news = []
        if not local_social_media_data:
            # Only use API news if local social media data is not available
            # This ensures we respect the --use_local_social_media flag
            try:
                from src.config.social_media_config import get_social_media_config
                config = get_social_media_config()

                if not config.should_use_local_social_media():
                    # User explicitly disabled local social media, use API news as fallback
                    company_news = get_company_news(
                        ticker=ticker,
                        end_date=end_date,
                        limit=50,
                        agent_name="social_media_analyst_agent",
                    )
                else:
                    # User wants local social media but no data available
                    progress.update_status("social_media_analyst_agent", ticker,
                                         f"Local social media mode enabled but no data found for {ticker}")
            except ImportError:
                # Fallback if config not available
                company_news = get_company_news(
                    ticker=ticker,
                    end_date=end_date,
                    limit=50,
                    agent_name="social_media_analyst_agent",
                )

        # Get insider trades as additional social sentiment indicator
        insider_trades = get_insider_trades(
            ticker=ticker,
            end_date=end_date,
            limit=20,
            agent_name="social_media_analyst_agent",
        )

        # Enhanced error handling and graceful degradation
        if not local_social_media_data and not company_news and not insider_trades:
            progress.update_status("social_media_analyst_agent", ticker, "Warning: No sentiment data found, providing neutral signal")

            # Provide a neutral signal with explanation when no data is available
            social_analysis[ticker] = {
                "signal": "neutral",
                "confidence": 0.0,
                "reasoning": {
                    "public_sentiment_signal": {
                        "signal": "neutral",
                        "details": f"No local social media data available for {ticker}. Local social media data directory may not exist or may be empty for the requested time period.",
                    },
                    "insider_activity_signal": {
                        "signal": "neutral",
                        "details": "No insider trading data available for analysis.",
                    },
                    "attention_signal": {
                        "signal": "neutral",
                        "details": "No social media attention data available for analysis.",
                    },
                    "sentiment_momentum_signal": {
                        "signal": "neutral",
                        "details": "No historical social media data available to assess sentiment momentum.",
                    },
                    "social_influence_signal": {
                        "signal": "neutral",
                        "details": "No social media data available to assess social influence factors.",
                    },
                },
            }
            continue

        progress.update_status("social_media_analyst_agent", ticker, "Preparing social sentiment data for LLM analysis")

        # Prepare social media/sentiment data for LLM analysis
        end_datetime = datetime.strptime(end_date, "%Y-%m-%d")
        recent_threshold = end_datetime - timedelta(days=7)  # Last 7 days

        # Initialize social sentiment data structure
        social_data = {
            "social_media_data": {
                "total_posts": len(local_social_media_data) if local_social_media_data else 0,
                "recent_posts": [],
                "sentiment_distribution": {"bullish": 0, "bearish": 0, "neutral": 0},
                "engagement_metrics": {"total_upvotes": 0, "total_comments": 0, "avg_engagement": 0},
                "platform_breakdown": {},
                "trending_topics": []
            },
            "news_sentiment_data": {
                "total_news": len(company_news) if company_news else 0,
                "recent_news": [],
                "sentiment_distribution": {"positive": 0, "negative": 0, "neutral": 0},
                "sentiment_trend": "stable"
            },
            "insider_activity_data": {
                "total_trades": len(insider_trades) if insider_trades else 0,
                "recent_trades": [],
                "buy_sell_ratio": 0,
                "insider_sentiment": "neutral"
            },
            "attention_metrics": {
                "news_frequency": 0,
                "social_media_frequency": len(local_social_media_data) if local_social_media_data else 0,
                "recent_activity_level": "normal",
                "buzz_indicators": []
            }
        }

        # Process local social media data if available
        if local_social_media_data:
            recent_posts = []
            platform_counts = {}
            total_engagement = 0

            for post in local_social_media_data:
                # Filter recent posts (within the time threshold)
                try:
                    post_date = datetime.strptime(post.get('created_time', ''), "%Y-%m-%d %H:%M:%S")
                    if post_date >= recent_threshold:
                        recent_posts.append(post)
                except:
                    # If date parsing fails, include the post anyway
                    recent_posts.append(post)

                # Count platform breakdown
                platform = post.get('platform', 'unknown')
                platform_counts[platform] = platform_counts.get(platform, 0) + 1

                # Aggregate engagement metrics
                engagement_score = post.get('engagement_score', 0)
                total_engagement += engagement_score

                # Count sentiment distribution
                sentiment = post.get('sentiment', 'neutral').lower()
                if sentiment == 'bullish':
                    social_data["social_media_data"]["sentiment_distribution"]["bullish"] += 1
                elif sentiment == 'bearish':
                    social_data["social_media_data"]["sentiment_distribution"]["bearish"] += 1
                else:
                    social_data["social_media_data"]["sentiment_distribution"]["neutral"] += 1

            # Store processed social media data
            social_data["social_media_data"]["recent_posts"] = recent_posts[:20]  # Limit to 20 most recent
            social_data["social_media_data"]["platform_breakdown"] = platform_counts
            social_data["social_media_data"]["engagement_metrics"]["total_upvotes"] = sum(post.get('upvotes', 0) for post in local_social_media_data)
            social_data["social_media_data"]["engagement_metrics"]["total_comments"] = sum(post.get('num_comments', 0) for post in local_social_media_data)
            social_data["social_media_data"]["engagement_metrics"]["avg_engagement"] = total_engagement / len(local_social_media_data) if local_social_media_data else 0

            # Extract trending topics from titles
            trending_topics = []
            for post in recent_posts[:10]:  # Top 10 recent posts
                title = post.get('title', '').lower()
                if ticker.lower() in title:
                    trending_topics.append(post.get('title', '')[:100])  # Truncate long titles
            social_data["social_media_data"]["trending_topics"] = trending_topics[:5]  # Top 5 trending topics

            # Update attention metrics
            social_data["attention_metrics"]["recent_activity_level"] = "high" if len(recent_posts) > 10 else "low" if len(recent_posts) < 3 else "normal"
            if len(recent_posts) > 15:
                social_data["attention_metrics"]["buzz_indicators"].append("high_social_media_activity")

        # Process news sentiment data (as proxy for social sentiment when no social media data)
        if company_news:
            recent_news = []
            older_news = []

            for news in company_news:
                if hasattr(news, 'date') and news.date:
                    try:
                        news_date = datetime.strptime(news.date, "%Y-%m-%d")
                        if news_date >= recent_threshold:
                            recent_news.append(news)
                        else:
                            older_news.append(news)
                    except:
                        older_news.append(news)
                else:
                    older_news.append(news)

            # Process recent news for sentiment analysis
            for news in recent_news[:15]:  # Limit to 15 most recent
                news_item = {
                    "date": getattr(news, 'date', None),
                    "title": getattr(news, 'title', None),
                    "sentiment": getattr(news, 'sentiment', None),
                    "source": getattr(news, 'source', None)
                }
                social_data["news_sentiment_data"]["recent_news"].append(news_item)

                # Count sentiment distribution
                sentiment = getattr(news, 'sentiment', 'neutral')
                if sentiment and sentiment.lower() == 'positive':
                    social_data["news_sentiment_data"]["sentiment_distribution"]["positive"] += 1
                elif sentiment and sentiment.lower() == 'negative':
                    social_data["news_sentiment_data"]["sentiment_distribution"]["negative"] += 1
                else:
                    social_data["news_sentiment_data"]["sentiment_distribution"]["neutral"] += 1

            # Determine sentiment trend
            if len(recent_news) > len(older_news[:len(recent_news)]):
                social_data["news_sentiment_data"]["sentiment_trend"] = "increasing_attention"
            elif len(recent_news) < len(older_news[:len(recent_news)]):
                social_data["news_sentiment_data"]["sentiment_trend"] = "decreasing_attention"

            social_data["attention_metrics"]["news_frequency"] = len(recent_news)
            social_data["attention_metrics"]["recent_activity_level"] = "high" if len(recent_news) > 5 else "low" if len(recent_news) < 2 else "normal"

        # Process insider trading data
        if insider_trades:
            buy_trades = 0
            sell_trades = 0
            total_buy_shares = 0
            total_sell_shares = 0

            for trade in insider_trades[:10]:  # Limit to 10 most recent trades
                trade_item = {
                    "date": getattr(trade, 'date', None),
                    "transaction_shares": getattr(trade, 'transaction_shares', None),
                    "transaction_type": getattr(trade, 'transaction_type', None),
                    "insider_name": getattr(trade, 'insider_name', None)
                }
                social_data["insider_activity_data"]["recent_trades"].append(trade_item)

                # Count buy/sell activity
                shares = getattr(trade, 'transaction_shares', 0)
                if shares and shares > 0:
                    buy_trades += 1
                    total_buy_shares += shares
                elif shares and shares < 0:
                    sell_trades += 1
                    total_sell_shares += abs(shares)

            # Calculate buy/sell ratio and sentiment
            if total_buy_shares + total_sell_shares > 0:
                social_data["insider_activity_data"]["buy_sell_ratio"] = total_buy_shares / (total_buy_shares + total_sell_shares)

            if buy_trades > sell_trades:
                social_data["insider_activity_data"]["insider_sentiment"] = "bullish"
            elif sell_trades > buy_trades:
                social_data["insider_activity_data"]["insider_sentiment"] = "bearish"
            else:
                social_data["insider_activity_data"]["insider_sentiment"] = "neutral"

        # Create LLM prompt for comprehensive social media analysis
        template = ChatPromptTemplate.from_messages([
            ("system", """You are an expert social media and sentiment analyst with deep knowledge of public perception analysis, social influence patterns, and crowd psychology in financial markets. Your task is to analyze the provided historical social sentiment data for {ticker} and generate a comprehensive trading recommendation.

**IMPORTANT: You are analyzing HISTORICAL LOCAL SOCIAL MEDIA DATA, not real-time API data.**

The social media data you receive comes from local historical archives that may have gaps in coverage. This data represents past social sentiment and discussions that occurred around the specified time period. Your analysis should account for:

- **Historical Context**: The data reflects past social sentiment, not current real-time discussions
- **Data Availability**: Social media data may not be available for every single day due to collection limitations
- **Temporal Relevance**: Consider how historical social sentiment patterns might indicate future market behavior
- **Coverage Gaps**: Missing data for certain periods should be noted but not prevent analysis of available data

Analyze the following aspects in detail:

1. **Historical Social Media Sentiment Analysis**: Analyze available Reddit posts, engagement metrics, sentiment distribution, and trending topics from the historical data
2. **Public Sentiment Analysis**: Evaluate overall public perception patterns, emotional indicators, and social mood trends from historical news and social media
3. **Insider Activity Analysis**: Assess insider trading patterns and their social/psychological implications in the historical context
4. **Attention Analysis**: Analyze historical public attention levels, buzz patterns, and viral potential across platforms
5. **Sentiment Momentum Analysis**: Examine historical sentiment changes, momentum shifts, and crowd behavior patterns
6. **Social Influence Analysis**: Consider historical social influence factors, opinion leaders, and network effects

Consider the following historical social sentiment data:
{social_data}

Key data interpretation guidelines for HISTORICAL data:
- If social_media_data has posts, prioritize social media sentiment over news sentiment
- Pay attention to engagement metrics (upvotes, comments) as indicators of historical community interest
- Consider platform-specific behavior (Reddit tends to be more retail investor focused)
- Trending topics can indicate past narratives or concerns that may have ongoing relevance
- Historical high social media activity combined with strong sentiment can indicate past momentum patterns
- Account for potential data gaps - focus analysis on available data rather than missing periods
- Consider how historical sentiment patterns might predict future market behavior
- Note the temporal context in your reasoning (e.g., "Based on historical social sentiment from [date range]...")

Based on your analysis of the historical data, provide a JSON response with the following structure:
{{
  "signal": "bullish/bearish/neutral",
  "confidence": 0-100,
  "reasoning": "detailed reasoning explaining your decision based on historical social sentiment patterns",
  "public_sentiment_analysis": "analysis of historical public sentiment and social perception patterns",
  "insider_activity_analysis": "analysis of historical insider trading patterns and implications",
  "attention_analysis": "analysis of historical public attention and buzz levels",
  "sentiment_momentum_analysis": "analysis of historical sentiment momentum and trend changes",
  "social_influence_analysis": "analysis of historical social influence factors and network effects"
}}

Focus on the most important historical social sentiment factors that drive market behavior and trading decisions. Consider both immediate historical social impact and longer-term sentiment trend patterns that may continue to influence future market behavior."""),
            ("user", "Please analyze the historical social sentiment data for {ticker} and provide your trading recommendation in JSON format based on the available historical social media data.")
        ])

        prompt = template.invoke({
            "ticker": ticker,
            "social_data": json.dumps(social_data, indent=2, default=str)
        })

        progress.update_status("social_media_analyst_agent", ticker, "Getting LLM social sentiment analysis")

        # Call LLM for analysis
        def create_default_social_signal():
            return SocialMediaAnalysisSignal(
                signal="neutral",
                confidence=0.0,
                reasoning="Error in LLM analysis; defaulting to neutral",
                public_sentiment_analysis="Unable to assess public sentiment",
                insider_activity_analysis="Unable to assess insider activity",
                attention_analysis="Unable to assess attention levels",
                sentiment_momentum_analysis="Unable to assess sentiment momentum",
                social_influence_analysis="Unable to assess social influence"
            )

        llm_result = call_llm(
            prompt=prompt,
            model_name=model_name,
            model_provider=model_provider,
            pydantic_model=SocialMediaAnalysisSignal,
            agent_name="social_media_analyst_agent",
            default_factory=create_default_social_signal,
        )

        progress.update_status("social_media_analyst_agent", ticker, "Processing LLM results")

        # Process LLM results and create structured reasoning
        reasoning = {
            "public_sentiment_signal": {
                "signal": llm_result.signal,
                "details": llm_result.public_sentiment_analysis,
            },
            "insider_activity_signal": {
                "signal": llm_result.signal,
                "details": llm_result.insider_activity_analysis,
            },
            "attention_signal": {
                "signal": llm_result.signal,
                "details": llm_result.attention_analysis,
            },
            "sentiment_momentum_signal": {
                "signal": llm_result.signal,
                "details": llm_result.sentiment_momentum_analysis,
            },
            "social_influence_signal": {
                "signal": llm_result.signal,
                "details": llm_result.social_influence_analysis,
            },
        }

        # Store the analysis results
        social_analysis[ticker] = {
            "signal": llm_result.signal,
            "confidence": llm_result.confidence,
            "reasoning": reasoning,
        }

        progress.update_status("social_media_analyst_agent", ticker, "Done")

    # Create the social media analysis message
    message = HumanMessage(
        content=json.dumps(social_analysis),
        name="social_media_analyst_agent",
    )

    # Print the reasoning if the flag is set
    if state["metadata"]["show_reasoning"]:
        show_agent_reasoning(social_analysis, "Social Media Analysis Agent")

    # Add the signal to the analyst_signals list
    state["data"]["analyst_signals"]["social_media_analyst_agent"] = social_analysis

    progress.update_status("social_media_analyst_agent", None, "Done")
    
    return {
        "messages": [message],
        "data": data,
    }
